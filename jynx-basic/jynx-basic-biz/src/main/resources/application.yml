server:
  port: 6063

spring:
  server:
    undertow:
      worker-threads: 100   # 默认 4核*8=32
      io-threads: 30
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:jynx-register}:${NACOS_PORT:8848}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml
  datasource:
    druid:
      max-active: 20
      min-idle: 10
      initial-size: 10
      max-wait: 30000
aliyun:
  sms:
    region-id: cn-beijing
    access-key-id: LTAI5tSbvRzzqJqvySa2nQC1
    access-key-secret: ******************************

