package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 快递客户订单
 *
 * <AUTHOR>
 * @date 2025-07-14 17:34:55
 */
@Data
@TenantTable
@TableName("tms_store_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "快递客户订单")
public class TmsStoreOrderEntity extends Model<TmsStoreOrderEntity> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

    /**
     * 推送外部单号
     */
    @Schema(description = "推送外部单号")
    private String externalOrderNumber;

    /**
     * 推送类型
     */
    @Schema(description = "推送类型:0:NB系统;1:小包系统")
    private Integer externalType;


    /**
     * 订单推送时间
     */
    @Schema(description = "订单推送时间")
    private LocalDateTime pushTime;

    /**
     * 跟踪单号
     */
    @Schema(description="跟踪单号")
    private String entrustedOrderNumber;

    /**
     * 门店客户id
     */
    @Schema(description="门店客户id")
    private Long storeCustomerId;

    /**
     * APIKEY
     */
    @Schema(description = "apikey")
    private Long subAccountApiId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Long storeId;

    /**
     * 运费总额
     */
    @Schema(description="运费总额")
    private BigDecimal totalFreightAmount;


    /**
     * 实付运费金额
     */
    @Schema(description = "实付运费金额")
    private BigDecimal payAmount;

    /**
     * 支付方式 0:线上支付 5:线下支付
     */
    @Schema(description = "支付方式")
    private Integer payType;

    /**
     * 订单状态：0: 待发货；5: 门店已取货；10: 服务商已取货；15: 已打印；20: 运输中；25: 已完成；30: 订单异常；35: 已取消；40: 已退款
     */
    @Schema(description="订单状态：0: 待发货；5: 门店已取货；10: 服务商已取货；15: 已打印；20: 运输中；25: 已完成；30: 订单异常；35: 已取消；40: 已退款")
    private Integer orderStatus;


    /**
     * 订单来源: 0:快递门店订单 5:代理商订单
     */
    @Schema(description = "订单来源:0:快递门店订单 5:代理商订单")
    private Integer sourceType;


    /**
     * 支付状态:0:待支付;5:已支付;15:支付失败
     */
    @Schema(description = "支付状态:0:待支付;5:已支付;15:支付失败")
    private Integer payStatus;

    /**
     * 支付流水单号
     */
    @Schema(description = "支付流水单号")
    private String payOrderId;

    /**
     * 是否POD/面签:0否;1是
     */
    @Schema(description="是否POD/面签:0否;1是")
    private Integer podSign;

    /**
     * 是否保险:0否;1是
     */
    @Schema(description="是否保险:0否;1是")
    private Integer insuranceSign;

    /**
     * 是否带磁带电:0否;1是
     */
    @Schema(description="是否带磁带电:0否;1是")
    private Integer batterySign;

    /**
     * 凭证打印状态:0-未打印;1-已打印
     */
    @Schema(description="凭证打印状态:0-未打印;1-已打印")
    private Integer printStatus;

    /**
     * 凭证打印时间
     */
    @Schema(description="凭证打印时间")
    private LocalDateTime printTime;

    /**
     * 发货方式0::送货到店;5:NB上门揽收;10:服务商上门揽收
     */
    @Schema(description="发货方式0::送货到店;5:NB上门揽收;10:服务商上门揽收")
    private Integer sendType;

    /**
     * 发货-联系人
     */
    @Schema(description="发货-联系人")
    private String shipperName;

    /**
     * 发货-联系电话
     */
    @Schema(description="发货-联系电话")
    private String shipperPhone;

    /**
     * 始发地
     */
    @Schema(description="始发地")
    private String shipperOrigin;

    /**
     * 始发地-邮编
     */
    @Schema(description="始发地-邮编")
    private String shipperPostalCode;

    /**
     * 始发地-地址
     */
    @Schema(description="始发地-地址")
    private String shipperAddress;

    /**
     * 收货-联系人
     */
    @Schema(description="收货-联系人")
    private String receiverName;

    /**
     * 收货-联系电话
     */
    @Schema(description="收货-联系电话")
    private String receiverPhone;

    /**
     * 收货地
     */
    @Schema(description="收货地")
    private String receiverDest;

    /**
     * 收货地-邮编
     */
    @Schema(description="收货地-邮编")
    private String receiverPostalCode;

    /**
     * 收货地-邮编-地址
     */
    @Schema(description="收货地-邮编-地址")
    private String receiverAddress;

    /**
     * 是否为主子单
     */
    @Schema(description = "主子单:0主单;1子单")
    private Integer subFlag;

    /**
     * 主跟踪单号
     */
    @Schema(description = "主跟踪单号")
    private String mainEntrustedOrder;

    /**
     * 核销状态:0否；1是
     */
    @Schema(description="核销状态:0否；1是")
    private Integer writeOffFlag;

    /**
     * 核销门店ID
     */
    @Schema(description="核销门店ID")
    private Long writeOffStoreId;

    /**
     * 面单地址
     */
    @Schema(description="面单地址")
    private String labelPath;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Integer revision;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记：0未删除，1已删除
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记：0未删除，1已删除")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}
