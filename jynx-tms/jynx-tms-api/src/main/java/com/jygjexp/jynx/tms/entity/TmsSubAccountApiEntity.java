package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TenantTable
@TableName("tms_sub_account_api")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "子账号api信息表")
public class TmsSubAccountApiEntity extends BaseLogicEntity<TmsSubAccountApiEntity> {
    /**
     * api信息主键
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="api信息主键")
    private Long id;
    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Long userId;
    /**
     * api名称
     */
    @Schema(description="api名称")
    private String apiName;
    /**
     * apikey
     */
    @Schema(description="apiKey")
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String apiKey;
    /**
     * 代理商id
     */
    @Schema(description="代理商id")
    private Long agentId;
    /**
     * 绑定服务商
     */
    @Schema(description="绑定服务商")
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private Long providerId;
    /**
     * 启用状态
     */
    @Schema(description="启用状态")
    private Boolean enabledStatus;

}
