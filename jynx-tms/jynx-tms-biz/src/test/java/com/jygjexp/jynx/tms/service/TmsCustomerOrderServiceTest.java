package com.jygjexp.jynx.tms.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class TmsCustomerOrderServiceTest {
    @Autowired
    private TmsCustomerOrderService tmsCustomerOrderService;

    @Test
    void getLatLngByAddress() {
        String destAddress = "Unit 601, 5089 Quebec St";
        String destination = "Canada/BC/Vancouver";
        String destPostalCode = "V5W 0E5";
        String latLngByAddress = tmsCustomerOrderService.getLatLngByAddress(destAddress, destination, destPostalCode);
        System.out.println(latLngByAddress);
    }
}
