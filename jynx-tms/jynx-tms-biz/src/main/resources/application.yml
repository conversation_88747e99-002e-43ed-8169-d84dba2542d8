server:
  undertow:
    worker-threads: 100   # 默认 4核*8=32
    io-threads: 30
  port: 9090


spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:jynx-register}:${NACOS_PORT:8848}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml
  datasource:
    druid:
        max-active: 80
        min-idle: 10
        initial-size: 30
        max-wait: 30000
