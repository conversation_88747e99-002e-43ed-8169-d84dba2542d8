<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - Neighbour Express</title>
    <style>
        a {
            text-decoration: none !important;
        }
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 200px;
            height: auto;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .success-title {
            font-size: 22px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 20px;
        }
        .success-image {
            max-width: 300px;
            width: 100%;
            height: auto;
            margin: 20px 0;
            border-radius: 8px;
        }
        .greeting {
            margin-bottom: 20px;
            background: linear-gradient(135deg, #f0fff4 0%, #e8f8e8 100%);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .greeting p {
            margin: 0 0 15px 0;
        }
        .success-message {
            font-size: 16px;
            color: #155724;
            font-weight: 500;
            background-color: rgba(40, 167, 69, 0.1);
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            border: 1px solid #28a745;
        }
        .next-step-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .next-step-title {
            font-size: 18px;
            font-weight: bold;
            color: #856404;
            margin-bottom: 15px;
        }
        .next-step-text {
            font-size: 14px;
            color: #856404;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        .verification-section {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 20px;
            margin: 15px 0;
        }
        .verification-code {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            color: #0c5460;
            padding: 15px;
            background-color: #ffffff;
            border: 2px dashed #007bff;
            border-radius: 5px;
            margin: 10px 0;
        }
        .order-details {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .detail-row {
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            display: inline-block;
            min-width: 140px;
        }
        .feedback-section {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .feedback-button {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 15px 0;
        }
        .feedback-button:hover {
            background-color: #218838;
        }
        .feedback-image {
            max-width: 450px;
            width: 100%;
            height: auto;
            margin-top: 20px;
            border-radius: 8px;
        }
        .footer {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .bullet-point {
            margin: 10px 0;
            padding-left: 20px;
        }
        @media (max-width: 600px) {
            body { padding: 10px; }
            .email-container { padding: 20px; }
            .logo { max-width: 150px; }
            .success-image { max-width: 100%; }
            .feedback-image { max-width: 100%; }
            .detail-label { min-width: auto; display: block; margin-bottom: 5px; }
            .greeting { padding: 15px; }
            .verification-code { font-size: 16px; }
        }
    </style>
</head>
<body>
<div class="email-container">
    <div class="header">
        <a href="https://www.neighbourexpress.com/" target="_blank">
            <img src="https://www.neighbourexpress.ca/assets/neighbourexpress_logo-e8c4e9b3.webp" alt="Neighbour Express" class="logo">
        </a>
        <div class="success-title">Success! Your order has been placed!</div>
        <img src="https://nbexpress.oss-us-east-1.aliyuncs.com/imageInformation/2054e696099a4498a0b6a10571b84716.png" alt="Order Success" class="success-image">
    </div>

    <div class="greeting">
        <p><strong>Hello <span th:text="${data.customerName}"></span>,</strong></p>
        <div class="success-message">
            Thank you for choosing Neighbour Express. We have successfully received your order through our online system.
        </div>
    </div>

    <div class="next-step-section">
        <div class="next-step-title">Next Step</div>
        <div class="next-step-text">
            Please bring your verification code / drop-off code together with your parcel to any Neighbour Express store (Not the Drop-off point, please check carefully).<br>
            Show the code to the store staff for processing.
        </div>

        <div class="verification-section">
            <div class="bullet-point">
                <strong>•</strong> You can present the code directly:
            </div>
            <div class="verification-code" th:text="${data.entrustedOrderNumber}"></div>
            </div>
            <div class="bullet-point">
                <strong>•</strong> Or download it from the website where you placed your order.
            </div>
        </div>
    </div>

    <div class="order-details">
        <p><strong>Here's the information we have for you:</strong></p>
        <!-- 送货地址 -->
        <div class="detail-row">
            <span class="detail-label">Delivery Address:</span>
            <strong th:text="${data.customerName}"></strong>
            &nbsp;&nbsp;&nbsp;
            <strong th:text="${data.deliveryAddress}"></strong>
        </div>

        <!-- 联系电话 -->
        <div class="detail-row">
            <span class="detail-label">Phone Number:</span>
            <strong th:text="${data.contactPhone}"></strong>
        </div>
    </div>

    <div class="feedback-section">
        <p><strong>How was your experience with us?</strong></p>
        <p>We would love to hear from you!</p>
        <a href="https://www.neighbourexpress.com/contactus" class="feedback-button">Share Your Feedback</a>
        <br/>
        <a href="https://www.neighbourexpress.com/about-neighbour-express" target="_blank">
            <img src="https://nbexpress.oss-us-east-1.aliyuncs.com/imageInformation/5c4473287f3142e79fd33459f37aed05.png" alt="Feedback" class="feedback-image">
        </a>
    </div>

    <div class="footer">
        <p>You received this message because your package is being handled by Neighbour Express.</p>
    </div>
</div>
</body>
</html>
