<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Cancellation Notification</title>
    <style>
        a {
            text-decoration: none !important;
        }
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 200px;
            height: auto;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .order-title {
            font-size: 22px;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 20px;
        }
        .greeting {
            margin-bottom: 20px;
            background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
        }
        .greeting p {
            margin: 0 0 15px 0;
        }
        .regret-message {
            font-size: 16px;
            color: #721c24;
            font-weight: 500;
            background-color: rgba(220, 53, 69, 0.08);
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            border: 1px solid #dc3545;
        }
        .order-details {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .detail-row {
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            display: inline-block;
            min-width: 140px;
        }
        .info-section {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-title {
            font-size: 16px;
            font-weight: bold;
            color: #0c5460;
            margin-bottom: 15px;
        }
        .reasons-list {
            margin: 15px 0;
            padding-left: 0;
            list-style: none;
        }
        .reasons-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            list-style: none;
        }
        .reasons-list li:before {
            content: "•";
            color: #007bff;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        .support-section {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .support-title {
            font-size: 16px;
            font-weight: bold;
            color: #0277bd;
            margin-bottom: 10px;
        }
        .contact-button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin-top: 15px;
        }
        .contact-button:hover {
            background-color: #0056b3;
        }
        .feedback-section {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .feedback-button {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 15px 0;
        }
        .feedback-button:hover {
            background-color: #218838;
        }
        .feedback-image {
            max-width: 450px;
            width: 100%;
            height: auto;
            margin-top: 20px;
            border-radius: 8px;
        }
        .footer {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        @media (max-width: 600px) {
            body { padding: 10px; }
            .email-container { padding: 20px; }
            .logo { max-width: 150px; }
            .feedback-image { max-width: 100%; }
            .detail-label { min-width: auto; display: block; margin-bottom: 5px; }
            .greeting { padding: 15px; }
        }
    </style>
</head>
<body>
<div class="email-container">
    <div class="header">
        <a href="https://www.neighbourexpress.com/" target="_blank">
            <img src="https://www.neighbourexpress.ca/assets/neighbourexpress_logo-e8c4e9b3.webp" alt="Neighbour Express" class="logo">
        </a>
        <div class="order-title">❌ Order Cancellation Notification</div>
    </div>

    <div class="greeting">
        <p><strong>Hello <span th:text="${data.customerName}"></span>,</strong></p>
        <div class="regret-message">
            We sincerely regret to inform you that your order has been cancelled. Please review the details below for your reference.
        </div>
    </div>

    <div class="order-details">
        <p><strong>Here are the order details on file:</strong></p>
        <!-- 送货地址 -->
        <div class="detail-row">
            <span class="detail-label">Delivery Address:</span>
            <strong th:text="${data.deliveryAddress}"></strong>
        </div>

        <!-- 联系电话 -->
        <div class="detail-row">
            <span class="detail-label">Phone Number:</span>
            <strong th:text="${data.contactPhone}"></strong>
        </div>
    </div>

    <div class="info-section">
        <div class="info-title">ℹ️ Why was my order cancelled?</div>
        <p>The cancellation may be due to one of the following reasons:</p>
        <ul class="reasons-list">
            <li>The package was not dropped off at a Neighbour Express store within the required time.</li>
            <li>Missing or incomplete shipping information.</li>
            <li>Cancellation requested by the customer.</li>
        </ul>
    </div>

    <div class="support-section">
        <div class="support-title">📞 Need Help?</div>
        <p>If you believe this cancellation is an error or you would like further assistance, please contact us through our support page</p>
        <a href="https://neighbourexpress.com/contactus" class="contact-button">Contact Us</a>
    </div>

    <div class="feedback-section">
        <p><strong>How was your experience with us?</strong></p>
        <p>We would love to hear from you!</p>
        <a href="https://www.neighbourexpress.com/contactus" class="feedback-button">Share Your Feedback</a>
        <br>
        <a href="https://www.neighbourexpress.com/about-neighbour-express" target="_blank">
            <img src="https://nbexpress.oss-us-east-1.aliyuncs.com/imageInformation/5c4473287f3142e79fd33459f37aed05.png" alt="Feedback" class="feedback-image">
        </a>
    </div>

    <div class="footer">
        <p>You received this message because your package is being handled by Neighbour Express.</p>
    </div>
</div>
</body>
</html>
