<div class="row" style="margin-left: 1px; margin-top:10px; height: 50px;">
    <div class="col-md-6" style="margin-bottom: 10px;">
        <span style="font-size: 30px;font-weight: bold;">{{app}}</span>
    </div>
</div>

<div class="separator"></div>
<div class="container-fluid">
    <div class="row" style="margin-top: 20px; margin-bottom: 20px;">
        <div class="col-md-12">
            <div class="card">
                <div class="inputs-header">
                    <span class="brand" style="font-size: 13px;">集群限流 - Token Server 总览</span>
                </div>

                <!-- error panel -->
                <div class="row clearfix" ng-if="loadError">
                    <div class="col-md-6 col-md-offset-3">
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <center>
                                    <p>{{loadError.message}}</p>
                                </center>
                            </div>
                        </div>
                    </div>
                </div>

                <!--.tools-header -->
                <div class="card-body" style="padding: 0px 0px;" ng-if="!loadError">
                    <form role="form" class="form-horizontal">
                        <div class="form-group" hidden>
                            <label class="col-sm-2 control-label">Token Server 列表</label>
                            <div class="col-sm-4">
                                <select ng-model="tmp.curChosenServer" ng-change="onChosenServerChange()"
                                        ng-options="serverEntity.id for serverEntity in serverVOList"
                                        class="form-control"></select>
                            </div>
                        </div>
                    </form>

                    <!-- table start -->
                    <table class="table" style="border-left: none; border-right:none;margin-top: 10px;">
                        <thead>
                        <tr style="background: #F3F5F7;">
                            <td style="width: 12%;">Server ID</td>
                            <td style="width: 5%;">Port</td>
                            <td style="width: 10%;">命名空间集合</td>
                            <td>总连接数</td>
                            <td>连接情况</td>
                            <td>QPS 总览</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="serverVO in clientVOList">
                            <td style="word-wrap:break-word;word-break:break-all;">{{serverVO.id}}</td>
                            <td>{{serverVO.port}}</td>
                            <td style="word-wrap:break-word;word-break:break-all;">
                                {{serverVO.state.namespaceSetStr}}
                            </td>
                            <td style="word-wrap:break-word;word-break:break-all;">
                                {{serverVO.connectedCount}}
                            </td>
                            <td>
                                <p ng-repeat="cg in serverVO.state.connection">
                                    namespace: {{cg.namespace}}, 连接数: {{cg.connectedCount}}, clients:
                                    {{generateConnectionSet(cg.connectionSet)}}
                                </p>
                            </td>
                            <td>
                                <p ng-repeat="crl in serverVO.state.requestLimitData">
                                    namespace: {{crl.namespace}}, 当前 QPS: {{crl.currentQps}}, 最大允许 QPS:
                                    {{crl.maxAllowedQps}}
                                </p>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <!-- .card-body -->
            </div>
            <!-- .card -->
        </div>
        <!-- .col-md-12 -->
    </div>
    <!-- -->
</div>
<!-- .container-fluid -->
